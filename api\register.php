<?php
/**
 * 用户注册API
 * 处理用户注册请求，包含数据验证、头像上传、自动分配ID等功能
 */

session_start();
require_once __DIR__ . '/../config/database.php';

header('Content-Type: application/json; charset=utf-8');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '不支持的请求方法']);
    exit;
}

try {
    // 检查数据库连接
    try {
        $db = DatabaseConfig::getInstance();
        // 测试数据库连接
        $db->query("SELECT 1");
    } catch (Exception $dbError) {
        error_log("数据库连接失败: " . $dbError->getMessage());
        echo json_encode([
            'success' => false,
            'message' => '系统暂时无法连接数据库，请稍后重试'
        ]);
        exit;
    }

    // 获取表单数据
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $nickname = trim($_POST['nickname'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirmPassword'] ?? '';
    
    // 数据验证
    $errors = [];
    
    // 验证用户名
    if (empty($username)) {
        $errors['username'] = '请输入用户名';
    } elseif (strlen($username) < 3) {
        $errors['username'] = '用户名至少需要3个字符';
    } elseif (!preg_match('/^[a-zA-Z0-9_\x{4e00}-\x{9fa5}]+$/u', $username)) {
        $errors['username'] = '用户名只能包含字母、数字、下划线和中文';
    } else {
        // 检查用户名是否已存在
        $existingUser = $db->fetchOne("SELECT id FROM users WHERE username = ?", [$username]);
        if ($existingUser) {
            $errors['username'] = '用户名已存在';
        }
    }
    
    // 验证邮箱
    if (empty($email)) {
        $errors['email'] = '请输入邮箱地址';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors['email'] = '请输入有效的邮箱地址';
    } else {
        // 检查邮箱是否已存在
        $existingEmail = $db->fetchOne("SELECT id FROM users WHERE email = ?", [$email]);
        if ($existingEmail) {
            $errors['email'] = '邮箱地址已被注册';
        }
    }
    
    // 验证昵称
    if (empty($nickname)) {
        $errors['nickname'] = '请输入昵称';
    } elseif (strlen($nickname) < 2) {
        $errors['nickname'] = '昵称至少需要2个字符';
    }
    
    // 验证密码
    if (empty($password)) {
        $errors['password'] = '请输入密码';
    } elseif (strlen($password) < 6) {
        $errors['password'] = '密码至少需要6个字符';
    }
    
    // 验证确认密码
    if (empty($confirmPassword)) {
        $errors['confirmPassword'] = '请确认密码';
    } elseif ($password !== $confirmPassword) {
        $errors['confirmPassword'] = '两次输入的密码不一致';
    }
    
    // 如果有验证错误，返回错误信息
    if (!empty($errors)) {
        echo json_encode([
            'success' => false,
            'message' => '请检查输入信息',
            'errors' => $errors
        ]);
        exit;
    }
    
    // 处理头像上传
    $avatarUrl = 'assets/images/default-avatar.png'; // 默认头像
    
    if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
        $uploadResult = handleAvatarUpload($_FILES['avatar']);
        if ($uploadResult['success']) {
            $avatarUrl = $uploadResult['url'];
        } else {
            echo json_encode([
                'success' => false,
                'message' => $uploadResult['message']
            ]);
            exit;
        }
    }
    
    // 开始数据库事务
    $db->execute("BEGIN");
    
    try {
        // 获取普通用户角色ID
        $userRole = $db->fetchOne("SELECT id FROM user_roles WHERE role_code = 'user'");
        $roleId = $userRole ? $userRole['id'] : 3; // 默认为3（普通用户）
        
        // 创建用户记录
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);

        $sql = "INSERT INTO users (username, email, password_hash, full_name, role_id, status, created_at)
                VALUES (?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP)";

        $result = $db->execute($sql, [
            $username,
            $email,
            $passwordHash,
            $nickname, // 使用昵称作为全名
            $roleId
        ]);

        if (!$result) {
            throw new Exception('创建用户失败');
        }

        // 获取插入的ID
        $userId = $db->lastInsertId();

        // 调试信息
        error_log("用户ID: " . $userId);

        if (!$userId || $userId == 0) {
            throw new Exception('获取用户ID失败');
        }

        // 创建用户个人资料记录
        $profileSql = "INSERT INTO user_profiles (user_id, nickname, avatar_url, created_at)
                       VALUES (?, ?, ?, CURRENT_TIMESTAMP)";

        $profileResult = $db->execute($profileSql, [
            $userId,
            $nickname,
            $avatarUrl
        ]);
        
        if (!$profileResult) {
            throw new Exception('创建用户资料失败');
        }
        
        // 提交事务
        $db->execute("COMMIT");
        
        // 发送注册通知给管理员
        sendRegistrationNotification($db, $userId, $username, $email, $nickname, $avatarUrl);
        
        // 记录注册日志
        error_log("新用户注册成功: ID={$userId}, 用户名={$username}, 邮箱={$email}");
        
        echo json_encode([
            'success' => true,
            'message' => '注册成功！请登录您的账户',
            'user_id' => $userId
        ]);
        
    } catch (Exception $e) {
        // 回滚事务
        $db->execute("ROLLBACK");
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("用户注册错误: " . $e->getMessage());
    error_log("错误堆栈: " . $e->getTraceAsString());

    // 根据错误类型提供更具体的错误信息
    $errorMessage = '注册过程中发生错误，请稍后重试';

    if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
        if (strpos($e->getMessage(), 'username') !== false) {
            $errorMessage = '用户名已存在，请选择其他用户名';
        } elseif (strpos($e->getMessage(), 'email') !== false) {
            $errorMessage = '邮箱地址已被注册，请使用其他邮箱';
        } else {
            $errorMessage = '用户信息重复，请检查用户名和邮箱';
        }
    } elseif (strpos($e->getMessage(), 'foreign key constraint') !== false ||
              strpos($e->getMessage(), 'Cannot add or update a child row') !== false) {
        $errorMessage = '用户角色配置错误，请联系管理员';
    } elseif (strpos($e->getMessage(), "Table") !== false && strpos($e->getMessage(), "doesn't exist") !== false) {
        $errorMessage = '数据库表不存在，请联系管理员初始化数据库';
    } elseif (strpos($e->getMessage(), 'Connection') !== false) {
        $errorMessage = '数据库连接失败，请稍后重试';
    }

    echo json_encode([
        'success' => false,
        'message' => $errorMessage,
        'debug_info' => [
            'error' => $e->getMessage(),
            'file' => basename($e->getFile()),
            'line' => $e->getLine()
        ]
    ]);
}

/**
 * 处理头像上传
 */
function handleAvatarUpload($file) {
    // 验证文件类型
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($file['type'], $allowedTypes)) {
        return ['success' => false, 'message' => '不支持的图片格式，请上传 JPG、PNG、GIF 或 WebP 格式的图片'];
    }
    
    // 验证文件大小 (2MB)
    if ($file['size'] > 2 * 1024 * 1024) {
        return ['success' => false, 'message' => '图片文件大小不能超过2MB'];
    }
    
    // 创建上传目录
    $uploadDir = __DIR__ . '/../uploads/avatars/';
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            return ['success' => false, 'message' => '无法创建上传目录'];
        }
    }
    
    // 生成唯一文件名
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid('avatar_') . '.' . $extension;
    $filepath = $uploadDir . $filename;
    
    // 移动上传文件
    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => false, 'message' => '文件上传失败'];
    }
    
    // 返回相对路径
    return ['success' => true, 'url' => 'uploads/avatars/' . $filename];
}

/**
 * 发送注册通知给管理员
 */
function sendRegistrationNotification($db, $userId, $username, $email, $nickname, $avatarUrl) {
    try {
        // 获取所有管理员用户（简化查询，直接根据role字段）
        $admins = $db->fetchAll(
            "SELECT id FROM users WHERE role IN ('admin', 'super_admin') AND status = 'active'"
        );

        // 如果没有找到管理员，尝试获取所有admin角色的用户
        if (empty($admins)) {
            $admins = $db->fetchAll("SELECT id FROM users WHERE role = 'admin'");
        }

        // 为每个管理员创建通知
        foreach ($admins as $admin) {
            $notificationSql = "INSERT INTO notifications (user_id, title, content, type, created_at, data)
                               VALUES (?, ?, ?, 'user_register', CURRENT_TIMESTAMP, ?)";

            $title = '新用户注册';
            $content = "用户 {$nickname}（{$username}）已成功注册，邮箱：{$email}";
            $data = json_encode([
                'user_id' => $userId,
                'username' => $username,
                'email' => $email,
                'nickname' => $nickname,
                'avatar_url' => $avatarUrl,
                'action' => 'view_user_profile'
            ]);

            $db->execute($notificationSql, [
                $admin['id'],
                $title,
                $content,
                $data
            ]);
        }

        // 同时添加到用户动态表（如果存在）
        try {
            $activitySql = "INSERT INTO user_activities (user_id, activity_type, description, created_at, data)
                           VALUES (?, 'user_register', ?, CURRENT_TIMESTAMP, ?)";

            $activityDescription = "新用户 {$nickname}（{$username}）注册了账号";
            $activityData = json_encode([
                'user_id' => $userId,
                'username' => $username,
                'nickname' => $nickname,
                'email' => $email
            ]);

            $db->execute($activitySql, [$userId, $activityDescription, $activityData]);
        } catch (Exception $e) {
            // 如果用户动态表不存在，忽略错误
            error_log("添加用户动态失败: " . $e->getMessage());
        }

    } catch (Exception $e) {
        error_log("发送注册通知失败: " . $e->getMessage());
        // 不抛出异常，避免影响注册流程
    }
}
?>
