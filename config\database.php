<?php
/**
 * 数据库配置文件
 * 支持自动检测MySQL端口(3306/3307)
 */

class DatabaseConfig {
    private static $instance = null;
    private $connection = null;
    
    // 数据库配置
    private $host = 'localhost';
    private $username = 'root';
    private $password = '';
    private $database = 'bitbear_system';
    private $charset = 'utf8mb4';
    private $ports = [3307, 3306]; // 支持的端口列表，优先使用3307

    // 服务器环境检测
    private function isServerEnvironment() {
        // 检查是否在服务器环境中
        return isset($_SERVER['SERVER_NAME']) &&
               (strpos($_SERVER['SERVER_NAME'], 'bitbear.top') !== false ||
                (isset($_SERVER['SERVER_ADDR']) && $_SERVER['SERVER_ADDR'] === '*************') ||
                (isset($_SERVER['HTTP_HOST']) && strpos($_SERVER['HTTP_HOST'], 'bitbear.top') !== false));
    }

    // 获取环境配置
    private function getEnvironmentConfig() {
        if ($this->isServerEnvironment()) {
            // 服务器环境配置
            return [
                'host' => 'localhost',
                'username' => 'bitbear_user',
                'password' => '309290133q',
                'database' => 'bitbear_website',
                'charset' => 'utf8mb4',
                'ports' => [3306]
            ];
        } else {
            // 本地环境配置
            return [
                'host' => $this->host,
                'username' => $this->username,
                'password' => $this->password,
                'database' => $this->database,
                'charset' => $this->charset,
                'ports' => $this->ports
            ];
        }
    }
    
    private function __construct() {
        // 设置时区为中国标准时间
        date_default_timezone_set('Asia/Shanghai');

        // 根据环境设置配置
        $config = $this->getEnvironmentConfig();
        $this->host = $config['host'];
        $this->username = $config['username'];
        $this->password = $config['password'];
        $this->database = $config['database'];
        $this->charset = $config['charset'];
        $this->ports = $config['ports'];

        $this->connect();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function connect() {
        // 首先尝试MySQL连接
        if ($this->tryMySQLConnection()) {
            return;
        }

        // MySQL连接失败，检查SQLite扩展是否可用
        if (!extension_loaded('pdo_sqlite')) {
            error_log("SQLite扩展未安装，无法使用SQLite作为备用方案");
            throw new Exception("数据库连接失败: MySQL不可用且SQLite扩展未安装。请安装MySQL服务器或启用SQLite扩展。");
        }

        // 使用SQLite作为备用
        $this->connectSQLite();
    }

    private function tryMySQLConnection() {
        $lastError = '';

        // 尝试不同端口连接
        foreach ($this->ports as $port) {
            try {
                $dsn = "mysql:host={$this->host};port={$port};charset={$this->charset}";
                $options = [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::ATTR_TIMEOUT => 3, // 减少连接超时时间
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset}",
                    PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
                    PDO::MYSQL_ATTR_FOUND_ROWS => true
                ];

                $this->connection = new PDO($dsn, $this->username, $this->password, $options);

                // 检查数据库是否存在，不存在则创建
                $this->createDatabaseIfNotExists();

                // 重新连接到指定数据库
                $dsn = "mysql:host={$this->host};port={$port};dbname={$this->database};charset={$this->charset}";
                $this->connection = new PDO($dsn, $this->username, $this->password, $options);

                error_log("MySQL数据库连接成功 - 端口: {$port}");
                return true;

            } catch (PDOException $e) {
                $lastError = $e->getMessage();
                error_log("MySQL端口 {$port} 连接失败: " . $lastError);

                // 检查是否是常见的连接问题
                if (strpos($lastError, 'Connection refused') !== false) {
                    error_log("提示：MySQL服务可能未启动，请检查 XAMPP/WAMP/MAMP 是否正在运行");
                } elseif (strpos($lastError, 'Access denied') !== false) {
                    error_log("提示：MySQL用户名或密码错误，请检查数据库配置");
                }

                continue;
            }
        }

        error_log("所有MySQL端口连接失败，最后错误: {$lastError}");
        error_log("将使用SQLite作为备用数据库");
        return false;
    }

    private function connectSQLite() {
        try {
            // 创建SQLite数据库文件路径
            $db_dir = __DIR__ . '/../database';

            // 确保目录存在并有正确权限
            if (!is_dir($db_dir)) {
                if (!mkdir($db_dir, 0755, true)) {
                    throw new Exception("无法创建数据库目录: " . $db_dir);
                }
            }

            // 检查目录权限
            if (!is_writable($db_dir)) {
                throw new Exception("数据库目录不可写: " . $db_dir);
            }

            $sqlite_path = $db_dir . '/bitbear_system.sqlite';

            // 如果数据库文件不存在，创建一个空文件
            if (!file_exists($sqlite_path)) {
                if (!touch($sqlite_path)) {
                    throw new Exception("无法创建数据库文件: " . $sqlite_path);
                }
                chmod($sqlite_path, 0644);
            }

            // 检查文件权限
            if (!is_readable($sqlite_path) || !is_writable($sqlite_path)) {
                throw new Exception("数据库文件权限不足: " . $sqlite_path);
            }

            $dsn = "sqlite:" . $sqlite_path;

            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_TIMEOUT => 30,
            ];

            $this->connection = new PDO($dsn, null, null, $options);

            // 设置SQLite特定的配置
            $this->connection->exec("PRAGMA foreign_keys = ON");
            $this->connection->exec("PRAGMA journal_mode = WAL");
            $this->connection->exec("PRAGMA synchronous = NORMAL");

            error_log("使用SQLite数据库作为备用方案: " . $sqlite_path);

            // 初始化SQLite数据库结构
            $this->initializeSQLiteDatabase();

        } catch (PDOException $e) {
            error_log("SQLite连接失败: " . $e->getMessage());
            throw new Exception("无法连接到数据库: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("SQLite初始化失败: " . $e->getMessage());
            throw $e;
        }
    }
    
    private function createDatabaseIfNotExists() {
        try {
            $sql = "CREATE DATABASE IF NOT EXISTS `{$this->database}` CHARACTER SET {$this->charset} COLLATE {$this->charset}_unicode_ci";
            $this->connection->exec($sql);
        } catch (PDOException $e) {
            error_log("创建数据库失败: " . $e->getMessage());
        }
    }

    private function initializeSQLiteDatabase() {
        try {
            // 检查是否已经初始化
            $tables = $this->connection->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll(PDO::FETCH_COLUMN);

            if (in_array('users', $tables)) {
                error_log("SQLite数据库已经初始化，跳过初始化步骤");
                return; // 已经初始化
            }

            error_log("开始初始化SQLite数据库结构...");
        } catch (PDOException $e) {
            error_log("检查数据库表失败: " . $e->getMessage());
            // 继续初始化，可能是第一次创建
        }

        try {
            // 创建用户角色表
            $this->connection->exec("
                CREATE TABLE user_roles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    role_code VARCHAR(50) UNIQUE NOT NULL,
                    role_name VARCHAR(100) NOT NULL,
                    description TEXT,
                    permissions TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ");
            error_log("用户角色表创建成功");

            // 创建用户表
            $this->connection->exec("
                CREATE TABLE users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    email VARCHAR(100) UNIQUE,
                    password_hash VARCHAR(255) NOT NULL,
                    full_name VARCHAR(100),
                    avatar VARCHAR(255),
                    role_id INTEGER,
                    status VARCHAR(20) DEFAULT 'active',
                    last_login DATETIME,
                    login_count INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (role_id) REFERENCES user_roles(id)
                )
            ");
            error_log("用户表创建成功");

            // 创建用户活动表
            $this->connection->exec("
                CREATE TABLE user_activities (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    activity_type VARCHAR(50) NOT NULL,
                    title VARCHAR(200) NOT NULL,
                    description TEXT,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            ");
            error_log("用户活动表创建成功");

            // 创建用户会话表
            $this->connection->exec("
                CREATE TABLE user_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    session_token VARCHAR(255) UNIQUE NOT NULL,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    expires_at DATETIME NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            ");
            error_log("用户会话表创建成功");

            // 创建用户资料表
            $this->connection->exec("
                CREATE TABLE user_profiles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    nickname VARCHAR(100),
                    avatar_url VARCHAR(500),
                    bio TEXT,
                    location VARCHAR(100),
                    website VARCHAR(200),
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            ");
            error_log("用户资料表创建成功");

            // 插入默认角色
            $this->connection->exec("
                INSERT INTO user_roles (role_code, role_name, description, permissions) VALUES
                ('super_admin', '超级管理员', '拥有系统所有权限', 'all'),
                ('admin', '管理员', '拥有大部分管理权限', 'dashboard,page_designer,content_management'),
                ('user', '普通用户', '基础用户权限', 'dashboard,personal_settings')
            ");
            error_log("默认角色插入成功");

            // 插入默认用户
            $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
            $this->connection->exec("
                INSERT INTO users (username, email, password_hash, full_name, role_id, status) VALUES
                ('admin', '<EMAIL>', '{$password_hash}', '系统管理员', 1, 'active')
            ");
            error_log("默认用户插入成功");

            // 创建主页内容表
            $this->createHomepageContentTables();

            // 创建社区相关表
            $this->createCommunityTables();

            error_log("SQLite数据库初始化完成");

        } catch (PDOException $e) {
            error_log("SQLite数据库初始化失败: " . $e->getMessage());
            throw new Exception("数据库初始化失败: " . $e->getMessage());
        }
    }

    // 创建主页内容相关表
    private function createHomepageContentTables() {
        // 执行主页内容表创建脚本
        $sqlFile = __DIR__ . '/../database/homepage_content_sqlite.sql';
        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);
            $statements = explode(';', $sql);

            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    try {
                        $this->connection->exec($statement);
                    } catch (PDOException $e) {
                        // 忽略已存在的表等错误
                        if (strpos($e->getMessage(), 'already exists') === false &&
                            strpos($e->getMessage(), 'UNIQUE constraint failed') === false) {
                            error_log("创建主页内容表失败: " . $e->getMessage());
                        }
                    }
                }
            }
            error_log("主页内容表创建完成");
        } else {
            error_log("主页内容表SQL文件不存在: " . $sqlFile);
        }
    }

    // 创建社区相关表
    private function createCommunityTables() {
        // 执行社区表创建脚本
        $sqlFile = __DIR__ . '/../database/create_community_tables.sql';
        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);

            // 将MySQL语法转换为SQLite语法
            $sql = $this->convertMySQLToSQLite($sql);

            $statements = explode(';', $sql);

            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    try {
                        $this->connection->exec($statement);
                    } catch (PDOException $e) {
                        // 忽略已存在的表等错误
                        if (strpos($e->getMessage(), 'already exists') === false &&
                            strpos($e->getMessage(), 'UNIQUE constraint failed') === false) {
                            error_log("创建社区表失败: " . $e->getMessage() . " SQL: " . $statement);
                        }
                    }
                }
            }
            error_log("社区表创建完成");
        } else {
            error_log("社区表SQL文件不存在: " . $sqlFile);
        }
    }

    // 将MySQL语法转换为SQLite语法
    private function convertMySQLToSQLite($sql) {
        // 替换AUTO_INCREMENT为AUTOINCREMENT
        $sql = str_replace('AUTO_INCREMENT', 'AUTOINCREMENT', $sql);

        // 替换INT为INTEGER
        $sql = preg_replace('/\bINT\b(?!\s+PRIMARY)/', 'INTEGER', $sql);

        // 替换LONGTEXT为TEXT
        $sql = str_replace('LONGTEXT', 'TEXT', $sql);

        // 替换VARCHAR为TEXT
        $sql = preg_replace('/VARCHAR\(\d+\)/', 'TEXT', $sql);

        // 替换DATETIME为TEXT
        $sql = str_replace('DATETIME', 'TEXT', $sql);

        // 替换TIMESTAMP为TEXT
        $sql = str_replace('TIMESTAMP', 'TEXT', $sql);

        // 移除ENGINE和CHARSET等MySQL特有语法
        $sql = preg_replace('/ENGINE=\w+/', '', $sql);
        $sql = preg_replace('/DEFAULT CHARSET=\w+/', '', $sql);
        $sql = preg_replace('/COLLATE=\w+/', '', $sql);

        // 移除INDEX语句（SQLite会自动处理）
        $sql = preg_replace('/,\s*INDEX\s+\w+\s*\([^)]+\)/', '', $sql);

        // 移除FOREIGN KEY约束（SQLite支持但语法不同）
        $sql = preg_replace('/,\s*FOREIGN KEY[^,)]+/', '', $sql);

        return $sql;
    }

    public function getConnection() {
        // 检查连接是否仍然有效
        if ($this->connection === null) {
            $this->connect();
        }
        
        try {
            $this->connection->query('SELECT 1');
        } catch (PDOException $e) {
            // 连接已断开，重新连接
            $this->connect();
        }
        
        return $this->connection;
    }
    
    public function query($sql, $params = []) {
        try {
            $stmt = $this->getConnection()->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("数据库查询错误: " . $e->getMessage());
            throw $e;
        }
    }
    
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    public function execute($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    public function lastInsertId() {
        return $this->getConnection()->lastInsertId();
    }
    
    public function beginTransaction() {
        return $this->getConnection()->beginTransaction();
    }
    
    public function commit() {
        return $this->getConnection()->commit();
    }
    
    public function rollback() {
        return $this->getConnection()->rollback();
    }
    
    // 初始化数据库表结构
    public function initializeDatabase() {
        $sqlFile = __DIR__ . '/../database/init.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception('数据库初始化文件不存在');
        }
        
        $sql = file_get_contents($sqlFile);
        $statements = explode(';', $sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                try {
                    $this->getConnection()->exec($statement);
                } catch (PDOException $e) {
                    // 忽略已存在的表等错误
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        error_log("执行SQL语句失败: " . $e->getMessage());
                    }
                }
            }
        }
    }
}

// 便捷函数
function db() {
    return DatabaseConfig::getInstance();
}
?>
